
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const dotenv = require('dotenv');
const connectDB = require('./config/db.js');
const step1Routes = require('./routes/step1.Routes.js');
const step2Routes = require('./routes/step2.Routes.js');
const chatRoutes = require('./routes/chat.Routes.js');
const verifyOtp = require('./controllers//verify.Otp.Controller.js');
const initializeChatSocket = require('./socket/chatSocket.js');


//swagger
const swaggerSpec = require('./appSwagger.js');
const swaggerHTML = require('./swaggerCustomUIHTML.js');

dotenv.config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*", 
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 5000;

// Connect to MongoDB
connectDB();

// Initialize Socket.IO for chat
const chatSocketHelpers = initializeChatSocket(io);

// Middlewares
app.use(cors());
app.use(express.json());

app.get('/swagger.json',(req,res)=>{
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.send(swaggerSpec);
})


app.get('/api-docs',(req,res)=>{
    res.setHeader('Content-Type', 'text/html');
    res.send(swaggerHTML);
})

// API Routes
app.use('/api/step1', step1Routes);
app.use('/api/step2', step2Routes);
app.use('/api/chat', chatRoutes);
// opt verification route
app.post('/api/verify-otp', verifyOtp);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Start server with Socket.IO support
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log(`Socket.IO enabled for real-time chat`);
});
