const Conversation = require('../models/Conversation');
const Message = require('../models/Message');
const Step2 = require('../models/Step2.Model');
const mongoose = require('mongoose');

// Create or get existing conversation between two users
const createOrGetConversation = async (req, res) => {
  try {
    const { participantId } = req.body;
    const { userId } = req.params;

    if (!participantId || !userId) {
      return res.status(400).json({
        success: false,
        message: 'Both userId and participantId are required'
      });
    }

    // Check if both users exist
    const [user1, user2] = await Promise.all([
      Step2.findById(userId),
      Step2.findById(participantId)
    ]);

    if (!user1 || !user2) {
      return res.status(404).json({
        success: false,
        message: 'One or both users not found'
      });
    }

    // Find existing conversation between these two users
    let conversation = await Conversation.findOne({
      participants: { $all: [userId, participantId] },
      isGroup: false
    }).populate('participants', 'username image')
      .populate('lastMessage');

    // If no conversation exists, create a new one
    if (!conversation) {
      conversation = new Conversation({
        participants: [userId, participantId],
        isGroup: false,
        unreadCounts: [
          { user: userId, count: 0 },
          { user: participantId, count: 0 }
        ]
      });
      await conversation.save();
      await conversation.populate('participants', 'username image');
    }

    res.status(200).json({
      success: true,
      data: conversation
    });
  } catch (error) {
    console.error('Error creating/getting conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get all conversations for a user
const getUserConversations = async (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    const conversations = await Conversation.find({
      participants: userId
    })
    .populate('participants', 'username image')
    .populate('lastMessage')
    .sort({ updatedAt: -1 });

    res.status(200).json({
      success: true,
      data: conversations
    });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Send a message
const sendMessage = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { senderId, content, messageType = 'text' } = req.body;

    if (!conversationId || !senderId || !content) {
      return res.status(400).json({
        success: false,
        message: 'Conversation ID, sender ID, and content are required'
      });
    }

    // Verify conversation exists and sender is a participant
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    if (!conversation.participants.includes(senderId)) {
      return res.status(403).json({
        success: false,
        message: 'Sender is not a participant in this conversation'
      });
    }

    // Create new message
    const message = new Message({
      conversation: conversationId,
      sender: senderId,
      content,
      messageType,
      deliveredTo: [senderId] // Mark as delivered to sender immediately
    });

    await message.save();
    await message.populate('sender', 'username image');

    // Update conversation's last message and unread counts
    conversation.lastMessage = message._id;

    // Increment unread count for other participants
    conversation.unreadCounts.forEach(unreadCount => {
      if (unreadCount.user.toString() !== senderId) {
        unreadCount.count += 1;
      }
    });

    await conversation.save();

    res.status(201).json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get messages for a conversation
const getMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    if (!conversationId) {
      return res.status(400).json({
        success: false,
        message: 'Conversation ID is required'
      });
    }

    const skip = (page - 1) * limit;

    const messages = await Message.find({
      conversation: conversationId,
      isDeleted: false
    })
    .populate('sender', 'username image')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));

    const totalMessages = await Message.countDocuments({
      conversation: conversationId,
      isDeleted: false
    });

    res.status(200).json({
      success: true,
      data: {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalMessages / limit),
          totalMessages,
          hasMore: skip + messages.length < totalMessages
        }
      }
    });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Mark messages as read
const markMessagesAsRead = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { userId } = req.body;

    if (!conversationId || !userId) {
      return res.status(400).json({
        success: false,
        message: 'Conversation ID and user ID are required'
      });
    }

    // Update all unread messages in the conversation
    await Message.updateMany(
      {
        conversation: conversationId,
        sender: { $ne: userId },
        readBy: { $ne: userId }
      },
      {
        $addToSet: { readBy: userId }
      }
    );

    // Reset unread count for this user
    await Conversation.updateOne(
      { _id: conversationId, 'unreadCounts.user': userId },
      { $set: { 'unreadCounts.$.count': 0 } }
    );

    res.status(200).json({
      success: true,
      message: 'Messages marked as read'
    });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Mark messages as delivered
const markMessagesAsDelivered = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { userId } = req.body;

    if (!conversationId || !userId) {
      return res.status(400).json({
        success: false,
        message: 'Conversation ID and user ID are required'
      });
    }

    // Update all undelivered messages in the conversation
    await Message.updateMany(
      {
        conversation: conversationId,
        sender: { $ne: userId },
        deliveredTo: { $ne: userId }
      },
      {
        $addToSet: { deliveredTo: userId }
      }
    );

    res.status(200).json({
      success: true,
      message: 'Messages marked as delivered'
    });
  } catch (error) {
    console.error('Error marking messages as delivered:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  createOrGetConversation,
  getUserConversations,
  sendMessage,
  getMessages,
  markMessagesAsRead,
  markMessagesAsDelivered
};
