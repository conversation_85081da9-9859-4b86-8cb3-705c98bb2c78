<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat System Example</title>
    <script src="https://cdn.socket.io/4.8.1/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chat-container {
            border: 1px solid #ddd;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
        }
        .message.sent {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .message.received {
            background-color: #e9ecef;
            color: black;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        input, button {
            padding: 10px;
        }
        input[type="text"] {
            flex: 1;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .typing-indicator {
            font-style: italic;
            color: #666;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>Chat System Example</h1>
    
    <div id="connection-section">
        <h3>Connect to Chat</h3>
        <input type="text" id="userId" placeholder="Enter your User ID" />
        <button onclick="connectToChat()">Connect</button>
    </div>

    <div id="chat-section" style="display: none;">
        <h3>Chat</h3>
        <div>
            <input type="text" id="participantId" placeholder="Enter participant User ID" />
            <button onclick="createConversation()">Start Conversation</button>
        </div>
        
        <div id="status" class="status"></div>
        
        <div id="chat-container" class="chat-container"></div>
        <div id="typing-indicator" class="typing-indicator"></div>
        
        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Type your message..." onkeypress="handleKeyPress(event)" />
            <button onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        let socket;
        let currentUserId;
        let currentConversationId;
        let typingTimer;

        // Connect to chat
        function connectToChat() {
            const userId = document.getElementById('userId').value.trim();
            if (!userId) {
                alert('Please enter a User ID');
                return;
            }

            currentUserId = userId;
            
            // Connect to Socket.IO server
            socket = io('http://localhost:5000'); // Change this to your server URL

            socket.on('connect', () => {
                console.log('Connected to server');
                socket.emit('join', { userId: currentUserId });
            });

            socket.on('connected', (data) => {
                document.getElementById('connection-section').style.display = 'none';
                document.getElementById('chat-section').style.display = 'block';
                updateStatus(`Connected as user: ${data.userId}`, false);
            });

            socket.on('error', (data) => {
                updateStatus(`Error: ${data.message}`, true);
            });

            socket.on('new_message', (data) => {
                displayMessage(data.message, data.message.sender._id === currentUserId);
                
                // Mark as delivered if not from current user
                if (data.message.sender._id !== currentUserId) {
                    markAsDelivered(data.conversationId);
                }
            });

            socket.on('user_typing', (data) => {
                if (data.userId !== currentUserId) {
                    const indicator = document.getElementById('typing-indicator');
                    if (data.isTyping) {
                        indicator.textContent = 'User is typing...';
                    } else {
                        indicator.textContent = '';
                    }
                }
            });

            socket.on('message_delivered', (data) => {
                console.log('Message delivered:', data);
            });

            socket.on('messages_read', (data) => {
                console.log('Messages read:', data);
            });

            socket.on('user_online', (data) => {
                console.log('User came online:', data.userId);
            });

            socket.on('user_offline', (data) => {
                console.log('User went offline:', data.userId);
            });
        }

        // Create or get conversation
        async function createConversation() {
            const participantId = document.getElementById('participantId').value.trim();
            if (!participantId) {
                alert('Please enter a participant User ID');
                return;
            }

            try {
                const response = await fetch(`http://localhost:5000/api/chat/conversations/${currentUserId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ participantId })
                });

                const data = await response.json();
                
                if (data.success) {
                    currentConversationId = data.data._id;
                    updateStatus(`Conversation created/found: ${currentConversationId}`, false);
                    
                    // Join the conversation room
                    socket.emit('join_conversation', {
                        conversationId: currentConversationId,
                        userId: currentUserId
                    });

                    // Load existing messages
                    loadMessages();
                } else {
                    updateStatus(`Error: ${data.message}`, true);
                }
            } catch (error) {
                updateStatus(`Error: ${error.message}`, true);
            }
        }

        // Load messages for current conversation
        async function loadMessages() {
            if (!currentConversationId) return;

            try {
                const response = await fetch(`http://localhost:5000/api/chat/conversations/${currentConversationId}/messages`);
                const data = await response.json();
                
                if (data.success) {
                    const chatContainer = document.getElementById('chat-container');
                    chatContainer.innerHTML = '';
                    
                    data.data.messages.forEach(message => {
                        displayMessage(message, message.sender._id === currentUserId);
                    });
                }
            } catch (error) {
                console.error('Error loading messages:', error);
            }
        }

        // Send message
        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const content = messageInput.value.trim();
            
            if (!content || !currentConversationId) {
                return;
            }

            socket.emit('send_message', {
                conversationId: currentConversationId,
                senderId: currentUserId,
                content: content,
                messageType: 'text'
            });

            messageInput.value = '';
            stopTyping();
        }

        // Display message in chat
        function displayMessage(message, isSent) {
            const chatContainer = document.getElementById('chat-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;
            
            const timestamp = new Date(message.createdAt).toLocaleTimeString();
            messageDiv.innerHTML = `
                <div><strong>${message.sender.username}</strong></div>
                <div>${message.content}</div>
                <div style="font-size: 0.8em; opacity: 0.7;">${timestamp}</div>
            `;
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Handle key press in message input
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            } else {
                startTyping();
            }
        }

        // Typing indicators
        function startTyping() {
            if (!currentConversationId) return;
            
            socket.emit('typing_start', {
                conversationId: currentConversationId,
                userId: currentUserId
            });

            clearTimeout(typingTimer);
            typingTimer = setTimeout(stopTyping, 3000);
        }

        function stopTyping() {
            if (!currentConversationId) return;
            
            socket.emit('typing_stop', {
                conversationId: currentConversationId,
                userId: currentUserId
            });
        }

        // Mark messages as delivered
        function markAsDelivered(conversationId) {
            fetch(`http://localhost:5000/api/chat/conversations/${conversationId}/delivered`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userId: currentUserId })
            });
        }

        // Update status display
        function updateStatus(message, isError) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = isError ? 'status error' : 'status';
        }
    </script>
</body>
</html>
